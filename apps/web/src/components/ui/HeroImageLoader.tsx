'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { LoadingSpinner } from './LoadingSpinner'
import { getRandomHeroImageUrl, getDefaultHeroImageUrl } from '@/lib/firebase/heroImages'
import { cn } from '@/lib/utils'

interface HeroImageLoaderProps {
  className?: string
  children?: React.ReactNode
  priority?: boolean
  onImageLoad?: (imageUrl: string) => void
}

export function HeroImageLoader({ 
  className, 
  children, 
  priority = false,
  onImageLoad 
}: HeroImageLoaderProps) {
  const [imageUrl, setImageUrl] = useState<string>(getDefaultHeroImageUrl())
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadRandomImage = async () => {
      try {
        setIsLoading(true)
        const randomImageUrl = await getRandomHeroImageUrl()
        setImageUrl(randomImageUrl)
        onImageLoad?.(randomImageUrl)
      } catch (error) {
        console.error('Error loading hero image:', error)
        setImageUrl(getDefaultHeroImageUrl())
      } finally {
        setIsLoading(false)
      }
    }

    loadRandomImage()
  }, [onImageLoad])

  const handleImageLoad = () => {
    setIsLoading(false)
  }

  const handleImageError = () => {
    console.error('Failed to load hero image, falling back to default')
    setImageUrl(getDefaultHeroImageUrl())
    setIsLoading(false)
  }

  return (
    <div className={cn('relative w-full h-full overflow-hidden', className)}>
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 z-10 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center">
          <div className="text-center text-white">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <p className="text-sm font-medium">Loading beautiful safari imagery...</p>
          </div>
        </div>
      )}

      {/* Hero Image */}
      <Image
        src={imageUrl}
        alt="Safari landscape"
        fill
        className="object-cover"
        priority={priority}
        onLoad={handleImageLoad}
        onError={handleImageError}
        sizes="100vw"
      />

      {/* Dark overlay for text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-40" />

      {/* Content overlay */}
      {children && (
        <div className="absolute inset-0 z-20">
          {children}
        </div>
      )}
    </div>
  )
}
